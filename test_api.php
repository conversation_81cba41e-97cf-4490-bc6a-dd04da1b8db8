<?php
/**
 * 测试微信登录API的绑定处理逻辑
 */

// 模拟API调用
function callAPI($action, $data) {
    $url = 'http://localhost/zdh/api/wechat_login_api.php';
    
    $postData = array_merge(['action' => $action], $data);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'response' => json_decode($response, true)
    ];
}

echo "=== 测试微信登录API绑定处理 ===\n\n";

// 测试绑定账号
echo "1. 测试绑定账号API:\n";
$bindResult = callAPI('bind_account', [
    'openid' => 'oPkSw67-LZZTiN7QtrLC_245Ag7A',
    'wechat_user_id' => '1',
    'username' => 'xiyue520',
    'password' => '8578369'
]);

echo "HTTP状态码: {$bindResult['http_code']}\n";
echo "响应内容: " . json_encode($bindResult['response'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

if ($bindResult['response']['status'] === 'success') {
    echo "✅ 绑定成功！\n";
    
    // 测试微信登录
    echo "\n2. 测试微信登录API:\n";
    $loginResult = callAPI('wechat_login', [
        'code' => 'test_code_123',  // 这是测试用的，实际需要真实的微信code
        'userInfo' => [
            'nickName' => '测试用户',
            'avatarUrl' => 'https://example.com/avatar.jpg'
        ]
    ]);
    
    echo "HTTP状态码: {$loginResult['http_code']}\n";
    echo "响应内容: " . json_encode($loginResult['response'], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
    
    if ($loginResult['response']['status'] === 'success') {
        echo "✅ 登录成功！\n";
    } else {
        echo "ℹ️ 登录状态: {$loginResult['response']['status']}\n";
    }
    
} else {
    echo "ℹ️ 绑定状态: {$bindResult['response']['status']}\n";
    echo "消息: {$bindResult['response']['message']}\n";
    
    if ($bindResult['response']['status'] === 'error') {
        if (strpos($bindResult['response']['message'], '已绑定') !== false) {
            echo "✅ API正确处理了重复绑定情况！\n";
        }
    }
}

echo "\n=== 测试完成 ===\n";
?>

<?php
/**
 * 微信登录功能数据库初始化脚本
 * 运行此脚本来创建微信登录相关的数据表
 */

require_once '../includes/config.php';

echo "开始初始化微信登录功能数据库...\n";

try {
    // 读取SQL文件
    $sqlFile = __DIR__ . '/create_wechat_tables.sql';
    if (!file_exists($sqlFile)) {
        throw new Exception("SQL文件不存在: $sqlFile");
    }
    
    $sql = file_get_contents($sqlFile);
    if ($sql === false) {
        throw new Exception("无法读取SQL文件");
    }
    
    // 分割SQL语句
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    $pdo->beginTransaction();
    
    foreach ($statements as $statement) {
        if (!empty($statement)) {
            echo "执行: " . substr($statement, 0, 50) . "...\n";
            $pdo->exec($statement);
        }
    }
    
    $pdo->commit();
    echo "✓ 微信登录功能数据库初始化完成！\n";
    
    // 检查表是否创建成功
    $tables = ['wechat_users', 'user_wechat_bindings', 'wechat_tokens', 'wechat_login_logs'];
    echo "\n检查数据表创建情况:\n";
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✓ $table - 创建成功\n";
        } else {
            echo "✗ $table - 创建失败\n";
        }
    }
    
    echo "\n下一步操作:\n";
    echo "1. 在 includes/config.php 中设置您的微信小程序 AppID 和 Secret\n";
    echo "2. 在微信小程序中集成登录功能\n";
    echo "3. 测试微信登录和账号绑定功能\n";
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo "✗ 初始化失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>

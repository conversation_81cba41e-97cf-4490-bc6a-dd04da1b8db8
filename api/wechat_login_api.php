<?php
/**
 * 微信小程序登录API接口
 * 支持微信登录、账号绑定、解绑等功能
 */

require_once '../includes/config.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 微信小程序配置 - 请在includes/config.php中设置实际值
define('WECHAT_APPID', defined('WECHAT_MINI_APPID') ? WECHAT_MINI_APPID : 'your_wechat_appid_here');
define('WECHAT_SECRET', defined('WECHAT_MINI_SECRET') ? WECHAT_MINI_SECRET : 'your_wechat_secret_here');

/**
 * 记录微信登录日志
 */
function recordWechatLoginLog($openid, $userId, $action, $result, $errorMessage = null) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO wechat_login_logs (openid, user_id, action, ip_address, user_agent, result, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $openid,
            $userId,
            $action,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $result,
            $errorMessage
        ]);
    } catch (Exception $e) {
        error_log("记录微信登录日志失败: " . $e->getMessage());
    }
}

/**
 * 生成安全Token
 */
function generateSecureToken($userId, $wechatUserId = null, $loginType = 'wechat') {
    global $pdo;
    
    try {
        // 生成唯一token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + 86400 * 7); // 7天有效期
        
        // 清除该用户的旧token
        $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 插入新token
        $stmt = $pdo->prepare("
            INSERT INTO wechat_tokens (user_id, wechat_user_id, token, login_type, expires_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $userId,
            $wechatUserId,
            $token,
            $loginType,
            $expiresAt,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        return [
            'token' => $token,
            'expires_at' => $expiresAt
        ];
    } catch (Exception $e) {
        error_log("生成Token失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 验证Token
 */
function verifyWechatToken($token) {
    global $pdo;
    
    try {
        // 清理过期token
        $pdo->exec("DELETE FROM wechat_tokens WHERE expires_at < NOW()");
        
        $stmt = $pdo->prepare("
            SELECT wt.*, u.username, u.role, u.is_station_staff, wu.openid, wu.nickname
            FROM wechat_tokens wt
            JOIN users u ON wt.user_id = u.id
            LEFT JOIN wechat_users wu ON wt.wechat_user_id = wu.id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $tokenData = $stmt->fetch();
        
        if ($tokenData) {
            // 更新最后使用时间
            $stmt = $pdo->prepare("UPDATE wechat_tokens SET last_used_at = NOW() WHERE token = ?");
            $stmt->execute([$token]);
        }
        
        return $tokenData;
    } catch (Exception $e) {
        error_log("Token验证失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 调用微信API获取用户信息
 */
function getWechatUserInfo($code) {
    // 1. 通过code获取session_key和openid
    $url = "https://api.weixin.qq.com/sns/jscode2session";
    $params = [
        'appid' => WECHAT_APPID,
        'secret' => WECHAT_SECRET,
        'js_code' => $code,
        'grant_type' => 'authorization_code'
    ];
    
    $response = file_get_contents($url . '?' . http_build_query($params));
    $data = json_decode($response, true);
    
    if (isset($data['errcode']) && $data['errcode'] !== 0) {
        return ['error' => $data['errmsg'] ?? '微信登录失败'];
    }
    
    return [
        'openid' => $data['openid'],
        'session_key' => $data['session_key'],
        'unionid' => $data['unionid'] ?? null
    ];
}

/**
 * 保存或更新微信用户信息
 */
function saveWechatUser($openid, $sessionKey, $unionid = null, $userInfo = null) {
    global $pdo;
    
    try {
        // 检查用户是否已存在
        $stmt = $pdo->prepare("SELECT id FROM wechat_users WHERE openid = ?");
        $stmt->execute([$openid]);
        $existingUser = $stmt->fetch();
        
        if ($existingUser) {
            // 更新现有用户
            $stmt = $pdo->prepare("
                UPDATE wechat_users 
                SET session_key = ?, unionid = ?, updated_at = NOW()
                WHERE openid = ?
            ");
            $stmt->execute([$sessionKey, $unionid, $openid]);
            return $existingUser['id'];
        } else {
            // 创建新用户
            $stmt = $pdo->prepare("
                INSERT INTO wechat_users (openid, unionid, session_key, nickname, avatar_url, gender, city, province, country)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $openid,
                $unionid,
                $sessionKey,
                $userInfo['nickName'] ?? null,
                $userInfo['avatarUrl'] ?? null,
                $userInfo['gender'] ?? 0,
                $userInfo['city'] ?? null,
                $userInfo['province'] ?? null,
                $userInfo['country'] ?? null
            ]);
            return $pdo->lastInsertId();
        }
    } catch (Exception $e) {
        error_log("保存微信用户信息失败: " . $e->getMessage());
        return false;
    }
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true) ?: $_POST;
$action = $input['action'] ?? '';

try {
    switch ($action) {
        case 'wechat_login':
            // 微信登录
            $code = $input['code'] ?? '';
            $userInfo = $input['userInfo'] ?? null;
            
            if (empty($code)) {
                throw new Exception('缺少微信授权码');
            }
            
            // 获取微信用户信息
            $wechatData = getWechatUserInfo($code);
            if (isset($wechatData['error'])) {
                throw new Exception($wechatData['error']);
            }
            
            $openid = $wechatData['openid'];
            $sessionKey = $wechatData['session_key'];
            $unionid = $wechatData['unionid'] ?? null;
            
            // 保存微信用户信息
            $wechatUserId = saveWechatUser($openid, $sessionKey, $unionid, $userInfo);
            if (!$wechatUserId) {
                throw new Exception('保存微信用户信息失败');
            }
            
            // 检查是否已绑定系统账号
            $stmt = $pdo->prepare("
                SELECT uwb.user_id, u.username, u.role, u.is_station_staff
                FROM user_wechat_bindings uwb
                JOIN users u ON uwb.user_id = u.id
                WHERE uwb.wechat_user_id = ? AND uwb.is_active = 1
            ");
            $stmt->execute([$wechatUserId]);
            $binding = $stmt->fetch();
            
            if ($binding) {
                // 已绑定，直接登录
                $tokenData = generateSecureToken($binding['user_id'], $wechatUserId, 'wechat');
                if (!$tokenData) {
                    throw new Exception('生成登录凭证失败');
                }
                
                recordWechatLoginLog($openid, $binding['user_id'], 'login', 'success');
                
                echo json_encode([
                    'status' => 'success',
                    'data' => [
                        'token' => $tokenData['token'],
                        'expires_at' => $tokenData['expires_at'],
                        'user' => [
                            'id' => $binding['user_id'],
                            'username' => $binding['username'],
                            'role' => $binding['role'],
                            'is_station_staff' => $binding['is_station_staff']
                        ],
                        'wechat' => [
                            'openid' => $openid,
                            'nickname' => $userInfo['nickName'] ?? ''
                        ]
                    ]
                ]);
            } else {
                // 未绑定，返回需要绑定的状态
                recordWechatLoginLog($openid, null, 'login', 'failed', '账号未绑定');
                
                echo json_encode([
                    'status' => 'need_bind',
                    'message' => '微信账号尚未绑定系统账号，请先绑定',
                    'data' => [
                        'openid' => $openid,
                        'wechat_user_id' => $wechatUserId,
                        'nickname' => $userInfo['nickName'] ?? ''
                    ]
                ]);
            }
            break;
            
        case 'bind_account':
            // 绑定系统账号
            $openid = $input['openid'] ?? '';
            $wechatUserId = $input['wechat_user_id'] ?? '';
            $username = $input['username'] ?? '';
            $password = $input['password'] ?? '';
            
            if (empty($openid) || empty($username) || empty($password)) {
                throw new Exception('参数不完整');
            }
            
            // 验证系统账号
            $stmt = $pdo->prepare("SELECT id, username, role, password, is_station_staff FROM users WHERE username = ?");
            $stmt->execute([$username]);
            $user = $stmt->fetch();
            
            if (!$user || !password_verify($password, $user['password'])) {
                recordWechatLoginLog($openid, null, 'bind', 'failed', '用户名或密码错误');
                throw new Exception('用户名或密码错误');
            }
            
            // 检查是否已经绑定其他微信账号
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as count 
                FROM user_wechat_bindings 
                WHERE user_id = ? AND is_active = 1
            ");
            $stmt->execute([$user['id']]);
            $bindCount = $stmt->fetchColumn();
            
            if ($bindCount > 0) {
                recordWechatLoginLog($openid, $user['id'], 'bind', 'failed', '该账号已绑定其他微信');
                throw new Exception('该系统账号已绑定其他微信账号');
            }
            
            // 创建绑定关系
            $stmt = $pdo->prepare("
                INSERT INTO user_wechat_bindings (user_id, wechat_user_id, bind_type, bind_ip)
                VALUES (?, ?, 'manual', ?)
            ");
            $stmt->execute([$user['id'], $wechatUserId, $_SERVER['REMOTE_ADDR'] ?? '']);
            
            // 生成登录token
            $tokenData = generateSecureToken($user['id'], $wechatUserId, 'wechat');
            if (!$tokenData) {
                throw new Exception('生成登录凭证失败');
            }
            
            recordWechatLoginLog($openid, $user['id'], 'bind', 'success');
            
            echo json_encode([
                'status' => 'success',
                'message' => '绑定成功',
                'data' => [
                    'token' => $tokenData['token'],
                    'expires_at' => $tokenData['expires_at'],
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'role' => $user['role'],
                        'is_station_staff' => $user['is_station_staff']
                    ]
                ]
            ]);
            break;
            
        case 'verify_token':
            // 验证token
            $token = $input['token'] ?? '';
            if (empty($token)) {
                throw new Exception('缺少token');
            }
            
            $tokenData = verifyWechatToken($token);
            if (!$tokenData) {
                throw new Exception('token无效或已过期');
            }
            
            echo json_encode([
                'status' => 'success',
                'data' => [
                    'user' => [
                        'id' => $tokenData['user_id'],
                        'username' => $tokenData['username'],
                        'role' => $tokenData['role'],
                        'is_station_staff' => $tokenData['is_station_staff']
                    ],
                    'login_type' => $tokenData['login_type'],
                    'expires_at' => $tokenData['expires_at']
                ]
            ]);
            break;
            
        case 'unbind_account':
            // 解绑账号（需要token验证）
            $token = $input['token'] ?? '';
            if (empty($token)) {
                throw new Exception('缺少token');
            }
            
            $tokenData = verifyWechatToken($token);
            if (!$tokenData) {
                throw new Exception('token无效或已过期');
            }
            
            // 删除绑定关系
            $stmt = $pdo->prepare("
                UPDATE user_wechat_bindings 
                SET is_active = 0, updated_at = NOW()
                WHERE user_id = ? AND wechat_user_id = ?
            ");
            $stmt->execute([$tokenData['user_id'], $tokenData['wechat_user_id']]);
            
            // 删除相关token
            $stmt = $pdo->prepare("DELETE FROM wechat_tokens WHERE user_id = ? AND wechat_user_id = ?");
            $stmt->execute([$tokenData['user_id'], $tokenData['wechat_user_id']]);
            
            recordWechatLoginLog($tokenData['openid'], $tokenData['user_id'], 'unbind', 'success');
            
            echo json_encode([
                'status' => 'success',
                'message' => '解绑成功'
            ]);
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
